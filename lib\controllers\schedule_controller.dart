import 'package:get/get.dart';
import '../models/showtime_model.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/screen_model.dart';
import '../services/schedule_service.dart';
import '../services/showtime_service.dart';
import '../services/theater_service.dart';
import '../services/screen_service.dart';
import '../services/firebase_movie_service.dart';

class ScheduleController extends GetxController {
  final ScheduleService _scheduleService = ScheduleService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final TheaterService _theaterService = TheaterService();
  final ScreenService _screenService = ScreenService();
  final FirebaseMovieService _movieService = FirebaseMovieService();

  // Observable variables
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs;
  final RxList<ScreenModel> screens = <ScreenModel>[].obs;
  final RxList<Movie> movies = <Movie>[].obs;
  final RxList<ShowtimeModel> showtimes = <ShowtimeModel>[].obs;
  final RxMap<String, List<ShowtimeModel>> scheduleByScreen =
      <String, List<ShowtimeModel>>{}.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxInt currentStep = 0.obs;

  // Cache for performance optimization
  final Map<String, TheaterModel?> _theaterCache = {};
  final Map<int, Movie?> _movieCache = {};
  final Map<String, ScreenModel?> _screenCache = {};

  // Individual loading states
  final RxBool isLoadingTheaters = false.obs;
  final RxBool isLoadingMovies = false.obs;
  final RxBool isLoadingScreens = false.obs;
  final RxBool isLoadingShowtimes = false.obs;

  // Selected values for scheduling
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);
  final Rx<TheaterModel?> selectedTheater = Rx<TheaterModel?>(null);
  final RxList<String> selectedScreenIds = <String>[].obs;
  final RxList<String> selectedDates = <String>[].obs;
  final RxList<String> selectedTimes = <String>[].obs;
  final Rx<DateTime> selectedDate = DateTime.now().obs;

  // Conflict detection
  final RxList<ScheduleConflict> conflicts = <ScheduleConflict>[].obs;
  final RxList<String> suggestedTimes = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    // Load in parallel for better performance
    final futures = <Future>[
      loadTheaters(),
      loadMovies(),
      loadAllScreens(),
      loadAllShowtimes(),
    ];

    try {
      await Future.wait(futures);
      print('ScheduleController: All initial data loaded successfully');
    } catch (e) {
      print('ScheduleController: Error loading initial data: $e');
      errorMessage.value = 'Lỗi tải dữ liệu ban đầu: $e';
    }
  }

  Future<void> loadTheaters() async {
    try {
      isLoadingTheaters.value = true;
      print('ScheduleController: Loading theaters...');
      final theaterList =
          await _theaterService.getAllTheaters(activeOnly: false);
      print('ScheduleController: Loaded ${theaterList.length} theaters');
      theaters.value = theaterList;

      // Update cache
      _theaterCache.clear();
      for (final theater in theaterList) {
        _theaterCache[theater.id] = theater;
        if (theaterList.indexOf(theater) < 5) {
          print(
              'ScheduleController: Theater - ${theater.name} (${theater.id}) - Active: ${theater.isActive}');
        }
      }
    } catch (e) {
      print('ScheduleController: Error loading theaters: $e');
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoadingTheaters.value = false;
    }
  }

  Future<void> loadMovies() async {
    try {
      isLoadingMovies.value = true;
      print('ScheduleController: Loading movies...');
      final movieList = await _movieService.getMovies();
      print('ScheduleController: Loaded ${movieList.length} movies');
      movies.value = movieList;

      // Update cache
      _movieCache.clear();
      for (final movie in movieList) {
        _movieCache[movie.id] = movie;
        if (movieList.indexOf(movie) < 3) {
          print('ScheduleController: Movie - ${movie.title} (${movie.id})');
        }
      }
    } catch (e) {
      print('ScheduleController: Error loading movies: $e');
      errorMessage.value = 'Không thể tải danh sách phim: $e';
    } finally {
      isLoadingMovies.value = false;
    }
  }

  Future<void> loadAllShowtimes() async {
    try {
      isLoadingShowtimes.value = true;
      print('ScheduleController: Loading all showtimes...');
      final showtimeList = await _showtimeService.getAllShowtimes();
      print('ScheduleController: Loaded ${showtimeList.length} showtimes');
      showtimes.value = showtimeList;
    } catch (e) {
      print('ScheduleController: Error loading showtimes: $e');
      errorMessage.value = 'Không thể tải danh sách lịch chiếu: $e';
    } finally {
      isLoadingShowtimes.value = false;
    }
  }

  Future<void> loadAllScreens() async {
    try {
      isLoadingScreens.value = true;
      print('ScheduleController: Loading all screens...');
      final screenList = await _screenService.getAllScreens(activeOnly: false);
      print('ScheduleController: Loaded ${screenList.length} screens');
      screens.value = screenList;

      // Update cache
      _screenCache.clear();
      for (final screen in screenList) {
        _screenCache[screen.id] = screen;
        if (screenList.indexOf(screen) < 3) {
          print(
              'ScheduleController: Screen - ${screen.name} (${screen.id}) - Active: ${screen.isActive}');
        }
      }
    } catch (e) {
      print('ScheduleController: Error loading all screens: $e');
      errorMessage.value = 'Không thể tải danh sách phòng chiếu: $e';
    } finally {
      isLoadingScreens.value = false;
    }
  }

  Future<void> loadScreensForTheater(String theaterId) async {
    try {
      isLoading.value = true;
      print('ScheduleController: Loading screens for theater: $theaterId');
      final screenList = await _screenService.getScreensByTheater(theaterId,
          activeOnly: false);
      print('ScheduleController: Loaded ${screenList.length} screens');
      screens.value = screenList;
      for (final screen in screenList) {
        print(
            'ScheduleController: Screen - ${screen.name} (${screen.id}) - Active: ${screen.isActive}');
      }
    } catch (e) {
      print('ScheduleController: Error loading screens: $e');
      errorMessage.value = 'Không thể tải danh sách phòng chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadScheduleForDate(String theaterId, String date) async {
    try {
      isLoading.value = true;
      final schedule =
          await _scheduleService.getTheaterScheduleForDate(theaterId, date);
      scheduleByScreen.value = schedule;
    } catch (e) {
      errorMessage.value = 'Không thể tải lịch chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Set selected movie
  void setSelectedMovie(Movie movie) {
    selectedMovie.value = movie;
    _clearConflicts();
  }

  // Set selected theater
  void setSelectedTheater(TheaterModel theater) {
    print(
        'ScheduleController: Setting selected theater: ${theater.name} (${theater.id})');
    selectedTheater.value = theater;
    selectedScreenIds.clear();
    _clearConflicts();

    // Load screens asynchronously without blocking
    Future.delayed(Duration.zero, () {
      loadScreensForTheater(theater.id);
    });
  }

  // Toggle screen selection
  void toggleScreenSelection(String screenId) {
    if (selectedScreenIds.contains(screenId)) {
      selectedScreenIds.remove(screenId);
    } else {
      selectedScreenIds.add(screenId);
    }
    _clearConflicts();
  }

  // Add date to selection
  void addSelectedDate(DateTime date) {
    final dateString = _formatDate(date);
    if (!selectedDates.contains(dateString)) {
      selectedDates.add(dateString);
    }
    _clearConflicts();
  }

  // Remove date from selection
  void removeSelectedDate(String date) {
    selectedDates.remove(date);
    _clearConflicts();
  }

  // Add time to selection
  void addSelectedTime(String time) {
    if (!selectedTimes.contains(time)) {
      selectedTimes.add(time);
    }
    _clearConflicts();
  }

  // Remove time from selection
  void removeSelectedTime(String time) {
    selectedTimes.remove(time);
    _clearConflicts();
  }

  // Check for conflicts before creating schedule
  Future<void> checkScheduleConflicts() async {
    if (!_isValidSelection()) {
      return;
    }

    try {
      isLoading.value = true;
      conflicts.clear();

      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      final conflictList = await _scheduleService.bulkCreateSchedule(
        movieId: selectedMovie.value!.id,
        theaterId: selectedTheater.value!.id,
        screenIds: selectedScreenIds.toList(),
        dates: selectedDates.toList(),
        times: selectedTimes.toList(),
        movieDuration: movieDuration,
      );

      conflicts.value = conflictList;

      if (conflicts.isNotEmpty) {
        // Collect all suggested times
        Set<String> allSuggestions = {};
        for (final conflict in conflicts) {
          allSuggestions.addAll(conflict.suggestedTimes);
        }
        suggestedTimes.value = allSuggestions.toList();
      }
    } catch (e) {
      errorMessage.value = 'Không thể kiểm tra xung đột lịch chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Create schedule (after conflict resolution)
  Future<bool> createSchedule() async {
    if (!_isValidSelection() || conflicts.isNotEmpty) {
      return false;
    }

    try {
      isLoading.value = true;

      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      for (final screenId in selectedScreenIds) {
        for (final date in selectedDates) {
          for (final time in selectedTimes) {
            final endTime =
                _scheduleService.calculateEndTime(time, movieDuration);

            final showtime = ShowtimeModel(
              id: '', // Will be set by Firestore
              movieId: selectedMovie.value!.id,
              theaterId: selectedTheater.value!.id,
              screenId: screenId,
              date: date,
              time: time,
              endTime: endTime,
              pricing: _getDefaultPricing(),
              availableSeats: _getScreenCapacity(screenId),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _showtimeService.createShowtime(showtime);
          }
        }
      }

      // Clear selections after successful creation
      _clearSelections();

      Get.snackbar(
        'Thành công',
        'Đã tạo lịch chiếu thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể tạo lịch chiếu: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get available time slots for a specific date and screen
  Future<List<String>> getAvailableTimeSlots(
      String screenId, String date) async {
    if (selectedMovie.value == null || selectedTheater.value == null) {
      return [];
    }

    try {
      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      return await _scheduleService.getAvailableTimeSlots(
        theaterId: selectedTheater.value!.id,
        screenId: screenId,
        date: date,
        movieDuration: movieDuration,
      );
    } catch (e) {
      errorMessage.value = 'Không thể tải khung giờ trống: $e';
      return [];
    }
  }

  // Get popular time slots
  Future<List<String>> getPopularTimeSlots() async {
    return await _scheduleService.getPopularTimeSlots();
  }

  // Helper methods
  bool _isValidSelection() {
    return selectedMovie.value != null &&
        selectedTheater.value != null &&
        selectedScreenIds.isNotEmpty &&
        selectedDates.isNotEmpty &&
        selectedTimes.isNotEmpty;
  }

  void _clearConflicts() {
    conflicts.clear();
    suggestedTimes.clear();
  }

  void _clearSelections() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedScreenIds.clear();
    selectedDates.clear();
    selectedTimes.clear();
    screens.clear();
    scheduleByScreen.clear();
    _clearConflicts();
  }

  ShowtimePricing _getDefaultPricing() {
    return ShowtimePricing(
      standard: 80000,
      vip: 120000,
      couple: 150000,
      student: 60000,
      senior: 60000,
    );
  }

  int _getScreenCapacity(String screenId) {
    final screen = screens.firstWhereOrNull((s) => s.id == screenId);
    return screen?.totalSeats ?? 100;
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Step validation methods
  bool get canProceedFromStep0 => selectedMovie.value != null;
  bool get canProceedFromStep1 =>
      selectedTheater.value != null && selectedTheater.value!.isActive;
  bool get canProceedFromStep2 => selectedScreenIds.isNotEmpty;
  bool get canProceedFromStep3 =>
      selectedDates.isNotEmpty && selectedTimes.isNotEmpty;
  bool get canProceedFromStep4 => _isValidSelection() && !hasConflicts;

  // Validation methods
  bool get hasConflicts => conflicts.isNotEmpty;
  bool get canCreateSchedule => _isValidSelection() && !hasConflicts;

  String get selectionSummary {
    if (!_isValidSelection()) return 'Chưa chọn đủ thông tin';

    return 'Phim: ${selectedMovie.value!.title}\n'
        'Rạp: ${selectedTheater.value!.name}\n'
        'Phòng: ${selectedScreenIds.length} phòng\n'
        'Ngày: ${selectedDates.length} ngày\n'
        'Giờ: ${selectedTimes.length} suất chiếu';
  }

  // Get schedule statistics
  Map<String, int> get scheduleStats {
    final totalShowtimes =
        selectedScreenIds.length * selectedDates.length * selectedTimes.length;

    return {
      'totalShowtimes': totalShowtimes,
      'conflictCount': conflicts.length,
      'successCount': totalShowtimes - conflicts.length,
    };
  }

  // CRUD operations for showtimes
  Future<bool> updateShowtime(ShowtimeModel showtime) async {
    try {
      isLoading.value = true;
      await _showtimeService.updateShowtime(showtime);

      // Update local list
      final index = showtimes.indexWhere((s) => s.id == showtime.id);
      if (index != -1) {
        showtimes[index] = showtime;
      }

      return true;
    } catch (e) {
      print('ScheduleController: Error updating showtime: $e');
      errorMessage.value = 'Không thể cập nhật lịch chiếu: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> deleteShowtime(String showtimeId) async {
    try {
      isLoading.value = true;
      await _showtimeService.deleteShowtime(showtimeId);

      // Remove from local list
      showtimes.removeWhere((s) => s.id == showtimeId);

      return true;
    } catch (e) {
      print('ScheduleController: Error deleting showtime: $e');
      errorMessage.value = 'Không thể xóa lịch chiếu: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> cancelShowtime(String showtimeId) async {
    try {
      isLoading.value = true;
      await _showtimeService.cancelShowtime(showtimeId);

      // Update local list
      final index = showtimes.indexWhere((s) => s.id == showtimeId);
      if (index != -1) {
        showtimes[index] =
            showtimes[index].copyWith(status: ShowtimeStatus.cancelled);
      }

      return true;
    } catch (e) {
      print('ScheduleController: Error cancelling showtime: $e');
      errorMessage.value = 'Không thể hủy lịch chiếu: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get movie by ID (with cache)
  Movie? getMovieById(int movieId) {
    // Try cache first
    if (_movieCache.containsKey(movieId)) {
      return _movieCache[movieId];
    }

    // Fallback to list search
    try {
      final movie = movies.firstWhere((movie) => movie.id == movieId);
      _movieCache[movieId] = movie; // Cache the result
      return movie;
    } catch (e) {
      _movieCache[movieId] = null; // Cache null result
      return null;
    }
  }

  // Get theater by ID (with cache)
  TheaterModel? getTheaterById(String theaterId) {
    // Try cache first
    if (_theaterCache.containsKey(theaterId)) {
      return _theaterCache[theaterId];
    }

    // Fallback to list search
    try {
      final theater = theaters.firstWhere((theater) => theater.id == theaterId);
      _theaterCache[theaterId] = theater; // Cache the result
      return theater;
    } catch (e) {
      _theaterCache[theaterId] = null; // Cache null result
      return null;
    }
  }

  // Get screen by ID (with cache)
  ScreenModel? getScreenById(String screenId) {
    // Try cache first
    if (_screenCache.containsKey(screenId)) {
      return _screenCache[screenId];
    }

    // Fallback to list search
    try {
      final screen = screens.firstWhere((screen) => screen.id == screenId);
      _screenCache[screenId] = screen; // Cache the result
      return screen;
    } catch (e) {
      _screenCache[screenId] = null; // Cache null result
      return null;
    }
  }

  // Update available seats for a specific showtime
  Future<bool> updateShowtimeAvailableSeats(
      String showtimeId, int availableSeats) async {
    try {
      isLoading.value = true;

      // Update in Firestore
      await _showtimeService.updateShowtimeAvailableSeats(
          showtimeId, availableSeats);

      // Update local list
      final index = showtimes.indexWhere((s) => s.id == showtimeId);
      if (index != -1) {
        showtimes[index] =
            showtimes[index].copyWith(availableSeats: availableSeats);
      }

      return true;
    } catch (e) {
      print('ScheduleController: Error updating showtime available seats: $e');
      errorMessage.value = 'Không thể cập nhật số ghế trống: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Fix all showtime data inconsistencies
  Future<void> fixAllShowtimeData() async {
    try {
      isLoading.value = true;
      await _showtimeService.fixAllShowtimeDataConsistency();
    } catch (e) {
      print('ScheduleController: Error fixing all showtime data: $e');
      errorMessage.value = 'Không thể sửa chữa dữ liệu: $e';
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  // Sync tickets and seats data
  Future<Map<String, int>> syncTicketsAndSeats() async {
    try {
      isLoading.value = true;
      return await _showtimeService.syncTicketsAndSeats();
    } catch (e) {
      print('ScheduleController: Error syncing tickets and seats: $e');
      errorMessage.value = 'Không thể đồng bộ dữ liệu: $e';
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  // Fix screen capacity inconsistencies
  Future<Map<String, int>> fixScreenCapacity() async {
    try {
      isLoading.value = true;
      return await _screenService.fixScreenCapacity();
    } catch (e) {
      print('ScheduleController: Error fixing screen capacity: $e');
      errorMessage.value = 'Không thể sửa chữa dung lượng phòng chiếu: $e';
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  // Reset form data
  void resetFormData() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedScreenIds.clear();
    selectedDates.clear();
    selectedTimes.clear();
    conflicts.clear();
    suggestedTimes.clear();
    currentStep.value = 0;
  }
}
